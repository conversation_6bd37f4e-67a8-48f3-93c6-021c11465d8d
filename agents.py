"""
智能体模块 - 定义各种专业化的智能体
"""
from typing import Dict, Any, List, Literal
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.prebuilt import create_react_agent
from langgraph.graph import MessagesState
from langgraph.types import Command
from search_tools import tavily_search, generate_search_queries
from config import Config
import logging

logger = logging.getLogger(__name__)

class AgentFactory:
    """智能体工厂类 - 创建和管理各种智能体"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.MODEL_BASE_URL,
            model=Config.MODEL_NAME,
            temperature=Config.AGENT_TEMPERATURE,
            max_tokens=Config.MAX_TOKENS
        )
    
    def create_search_agent(self) -> Any:
        """创建搜索智能体"""
        system_prompt = """你是一个专业的搜索智能体，负责执行网络搜索任务。

你的职责：
1. 根据用户查询生成有效的搜索关键词
2. 执行搜索并获取相关信息
3. 对搜索结果进行初步筛选和评估
4. 如果需要更多信息，可以进行多轮搜索

搜索策略：
- 使用不同的关键词组合进行搜索
- 关注搜索结果的时效性和权威性
- 避免重复搜索相同的内容

请用中文回复。"""

        return create_react_agent(
            self.llm,
            tools=[tavily_search, generate_search_queries]
        )
    
    def create_analysis_agent(self) -> Any:
        """创建分析智能体"""
        system_prompt = """你是一个专业的信息分析智能体，负责分析和整理搜索结果。

你的职责：
1. 分析搜索结果的质量和相关性
2. 提取关键信息和要点
3. 识别信息中的模式和趋势
4. 发现信息缺口，建议进一步搜索的方向

分析原则：
- 客观、准确地分析信息
- 区分事实和观点
- 注意信息的时效性和来源可靠性
- 提供结构化的分析结果

请用中文回复。"""

        return create_react_agent(
            self.llm,
            tools=[]
        )
    
    def create_synthesis_agent(self) -> Any:
        """创建综合智能体"""
        system_prompt = """你是一个专业的信息综合智能体，负责整合和总结所有收集到的信息。

你的职责：
1. 整合来自多个搜索的信息
2. 去除重复和冗余内容
3. 按逻辑结构组织信息
4. 生成清晰、全面的最终报告

综合原则：
- 确保信息的完整性和准确性
- 使用清晰的结构和格式
- 突出重点和关键发现
- 提供可操作的见解和建议

输出格式：
- 使用标题和子标题组织内容
- 包含关键要点的总结
- 提供信息来源的引用

请用中文回复。"""

        return create_react_agent(
            self.llm,
            tools=[]
        )

def create_handoff_tool(agent_name: str, description: str = None):
    """创建智能体切换工具"""
    from langchain_core.tools import tool
    
    @tool
    def handoff_tool() -> Command:
        """切换到指定的智能体"""
        return Command(
            goto=agent_name,
            update={"current_agent": agent_name}
        )
    
    handoff_tool.name = f"transfer_to_{agent_name}"
    handoff_tool.description = description or f"切换到{agent_name}智能体"
    
    return handoff_tool

# 创建智能体工厂实例
agent_factory = AgentFactory()

# 创建切换工具
transfer_to_search = create_handoff_tool(
    "search_agent", 
    "当需要执行搜索任务时切换到搜索智能体"
)

transfer_to_analysis = create_handoff_tool(
    "analysis_agent", 
    "当需要分析搜索结果时切换到分析智能体"
)

transfer_to_synthesis = create_handoff_tool(
    "synthesis_agent", 
    "当需要综合整理信息时切换到综合智能体"
)

def create_supervisor_agent() -> Any:
    """创建监督智能体"""
    system_prompt = """你是一个智能的任务协调者，负责管理多个专业智能体来完成复杂的搜索和分析任务。

可用的智能体：
1. search_agent - 负责执行网络搜索
2. analysis_agent - 负责分析搜索结果
3. synthesis_agent - 负责综合整理信息

工作流程：
1. 接收用户查询后，首先分析任务需求
2. 将任务分配给合适的智能体
3. 监控任务执行进度
4. 协调智能体之间的协作
5. 确保最终输出质量

决策原则：
- 根据任务类型选择合适的智能体
- 确保信息收集的全面性
- 保证分析的深度和准确性
- 提供结构化的最终结果

请用中文回复，并根据任务需要合理分配工作。"""

    llm = ChatOpenAI(
        api_key=Config.OPENAI_API_KEY,
        base_url=Config.MODEL_BASE_URL,
        model=Config.MODEL_NAME,
        temperature=Config.AGENT_TEMPERATURE,
        max_tokens=Config.MAX_TOKENS
    )

    return create_react_agent(
        llm,
        tools=[transfer_to_search, transfer_to_analysis, transfer_to_synthesis]
    )
