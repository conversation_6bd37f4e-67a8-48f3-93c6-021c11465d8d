# 多智能体搜索助手

基于LangGraph和Streamlit构建的智能搜索应用，使用多个专业化智能体协作完成复杂的搜索和信息整理任务。

## 功能特点

- 🤖 **多智能体协作**: 使用监督者模式协调搜索、分析和综合智能体
- 🔍 **多轮搜索**: 支持迭代式搜索，逐步完善搜索结果
- 📊 **智能分析**: 自动分析搜索结果，提取关键信息
- 📝 **结果整理**: 生成结构化的综合报告
- 🌐 **中文界面**: 完全中文化的用户界面
- ⚙️ **参数配置**: 可调节的搜索参数和模型设置

## 技术架构

### 核心组件

1. **配置管理** (`config.py`)
   - 环境变量管理
   - API密钥配置
   - 系统参数设置

2. **搜索工具** (`search_tools.py`)
   - Tavily搜索引擎集成
   - 搜索结果去重和排序
   - 查询优化和建议生成

3. **智能体系统** (`agents.py`)
   - 搜索智能体：执行网络搜索
   - 分析智能体：分析搜索结果
   - 综合智能体：整理最终报告
   - 监督智能体：协调整个流程

4. **工作流引擎** (`workflow.py`)
   - LangGraph状态图管理
   - 智能体间的协作流程
   - 错误处理和容错机制

5. **用户界面** (`streamlit_app.py`)
   - Streamlit Web界面
   - 实时搜索进度显示
   - 参数配置面板

6. **工具模块** (`utils.py`)
   - 结果处理和格式化
   - 查询预处理
   - 显示格式化

## 安装和配置

### 1. 环境要求

- Python 3.11+
- uv包管理器

### 2. 安装依赖

```bash
# 安装依赖
uv add langgraph streamlit python-dotenv tavily-python openai
```

### 3. 配置环境变量

`.env`文件已配置以下参数：

```env
# OpenAI API 配置 (使用ModelScope)
OPENAI_API_KEY=ms-b2129be5-2245-4c65-9a00-b7a4f4eeaa10
MODEL_BASE_URL=https://api-inference.modelscope.cn/v1/
MODEL_NAME=Qwen/Qwen3-Coder-480B-A35B-Instruct
MODEL_PROVIDER=ModelScope

# Tavily 搜索引擎 API
TAVILY_API_KEY=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
```

## 使用方法

### 启动应用

```bash
streamlit run streamlit_app.py
```

### 使用流程

1. **配置参数**: 在侧边栏调整搜索轮数、结果数量等参数
2. **输入查询**: 在主界面输入您的搜索问题
3. **开始搜索**: 点击"开始搜索"按钮
4. **查看结果**: 系统将显示搜索进度和最终结果
5. **历史管理**: 可以查看和重用历史搜索

### 示例查询

- "人工智能在医疗领域的最新应用"
- "区块链技术的发展趋势"
- "新能源汽车市场分析"
- "量子计算的技术突破"

## 系统架构

```
用户查询 → 监督智能体 → 搜索智能体 → 分析智能体 → 综合智能体 → 最终报告
    ↓           ↓            ↓            ↓            ↓
  Streamlit   LangGraph    Tavily API   LLM分析     结果整理
```

## 配置说明

### 搜索参数

- **最大搜索轮数**: 控制智能体执行搜索的轮数（1-5轮）
- **每轮最大结果数**: 每轮搜索返回的结果数量（5-20个）
- **创造性程度**: 控制AI回答的创造性（0.0-1.0）

### 模型配置

- **模型名称**: Qwen/Qwen3-Coder-480B-A35B-Instruct
- **API提供商**: ModelScope
- **温度参数**: 可调节的创造性程度

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查`.env`文件是否存在
   - 确认API密钥是否正确

2. **搜索失败**
   - 检查网络连接
   - 验证Tavily API密钥
   - 尝试简化搜索查询

3. **模型调用错误**
   - 确认OpenAI API配置
   - 检查ModelScope服务状态

### 日志查看

应用会在控制台输出详细的日志信息，包括：
- 搜索进度
- 错误信息
- 性能统计

## 开发说明

### 扩展功能

1. **添加新的搜索引擎**
   - 在`search_tools.py`中添加新的搜索客户端
   - 实现统一的搜索接口

2. **自定义智能体**
   - 在`agents.py`中定义新的智能体
   - 在工作流中添加相应的节点

3. **增强UI功能**
   - 修改`streamlit_app.py`添加新的界面元素
   - 使用`utils.py`中的格式化工具

### 性能优化

- 使用缓存减少重复搜索
- 实现异步处理提高响应速度
- 优化结果去重和排序算法

## 许可证

本项目采用MIT许可证。