"""
搜索工具模块 - 封装各种搜索功能
"""
import asyncio
from typing import List, Dict, Any, Optional
from tavily import TavilyClient
from langchain_core.tools import tool
from config import Config
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SearchManager:
    """搜索管理器 - 统一管理所有搜索操作"""
    
    def __init__(self):
        self.tavily_client = TavilyClient(api_key=Config.TAVILY_API_KEY)
        self.search_history = []
    
    async def web_search(self, query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """执行网络搜索"""
        try:
            max_results = max_results or Config.MAX_SEARCH_RESULTS
            
            # 记录搜索历史
            self.search_history.append({
                "query": query,
                "timestamp": time.time()
            })
            
            # 执行搜索
            response = self.tavily_client.search(
                query=query,
                max_results=max_results,
                include_answer=True,
                include_raw_content=True
            )
            
            # 处理搜索结果
            results = []
            for result in response.get('results', []):
                processed_result = {
                    'title': result.get('title', ''),
                    'url': result.get('url', ''),
                    'content': result.get('content', ''),
                    'score': result.get('score', 0),
                    'published_date': result.get('published_date', ''),
                    'raw_content': result.get('raw_content', '')[:1000]  # 限制长度
                }
                results.append(processed_result)
            
            logger.info(f"搜索完成: {query}, 找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {query}, 错误: {str(e)}")
            return []
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """生成搜索建议"""
        suggestions = []
        
        # 基于查询生成相关搜索建议
        base_suggestions = [
            f"{query} 最新发展",
            f"{query} 详细分析",
            f"{query} 相关案例",
            f"{query} 技术原理",
            f"{query} 应用场景"
        ]
        
        suggestions.extend(base_suggestions)
        return suggestions[:5]  # 返回前5个建议
    
    def deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get('url', '')
            if url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results
    
    def rank_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """对搜索结果进行排序"""
        # 简单的排序策略：基于分数和内容相关性
        def calculate_relevance(result):
            score = result.get('score', 0)
            title = result.get('title', '').lower()
            content = result.get('content', '').lower()
            query_lower = query.lower()
            
            # 标题匹配加分
            title_match = query_lower in title
            content_match = query_lower in content
            
            relevance_score = score
            if title_match:
                relevance_score += 0.3
            if content_match:
                relevance_score += 0.2
                
            return relevance_score
        
        return sorted(results, key=calculate_relevance, reverse=True)

# 创建全局搜索管理器实例
search_manager = SearchManager()

@tool
def tavily_search(query: str) -> str:
    """
    使用Tavily进行网络搜索的工具函数

    Args:
        query: 搜索查询字符串

    Returns:
        格式化的搜索结果字符串
    """
    # 同步调用搜索功能
    try:
        response = search_manager.tavily_client.search(
            query=query,
            max_results=Config.MAX_SEARCH_RESULTS,
            include_answer=True,
            include_raw_content=True
        )

        results = []
        for result in response.get('results', []):
            processed_result = {
                'title': result.get('title', ''),
                'url': result.get('url', ''),
                'content': result.get('content', ''),
                'score': result.get('score', 0),
                'published_date': result.get('published_date', ''),
                'raw_content': result.get('raw_content', '')[:1000]
            }
            results.append(processed_result)

        logger.info(f"搜索完成: {query}, 找到 {len(results)} 个结果")
    except Exception as e:
        logger.error(f"搜索失败: {query}, 错误: {str(e)}")
        results = []
    
    if not results:
        return f"搜索 '{query}' 没有找到相关结果。"
    
    # 格式化结果
    formatted_results = f"搜索 '{query}' 的结果:\n\n"
    
    for i, result in enumerate(results[:5], 1):  # 只显示前5个结果
        formatted_results += f"{i}. **{result['title']}**\n"
        formatted_results += f"   链接: {result['url']}\n"
        formatted_results += f"   内容: {result['content'][:200]}...\n"
        if result.get('published_date'):
            formatted_results += f"   发布时间: {result['published_date']}\n"
        formatted_results += "\n"
    
    return formatted_results

@tool
def generate_search_queries(topic: str, num_queries: int = 3) -> List[str]:
    """
    基于主题生成多个搜索查询
    
    Args:
        topic: 主题
        num_queries: 生成查询数量
        
    Returns:
        搜索查询列表
    """
    queries = [
        f"{topic} 最新动态",
        f"{topic} 技术发展",
        f"{topic} 应用案例",
        f"{topic} 市场趋势",
        f"{topic} 专家观点"
    ]
    
    return queries[:num_queries]
