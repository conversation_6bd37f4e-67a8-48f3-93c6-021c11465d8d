"""
测试搜索功能的简单脚本
"""
from workflow import multi_agent_workflow
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def test_search():
    """测试搜索功能"""
    print("🔍 开始测试搜索功能...")
    
    # 测试查询 - 使用更具体的查询来测试深度搜索
    test_queries = [
        "2024年中国人工智能大模型发展现状与技术突破"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*50}")
        print(f"测试 {i}: {query}")
        print('='*50)
        
        try:
            # 执行深度搜索
            results = multi_agent_workflow.run_search(query, max_rounds=5)
            
            if results.get("error"):
                print(f"❌ 搜索失败: {results['error']}")
            else:
                print(f"✅ 搜索成功!")
                print(f"📊 找到结果: {len(results.get('search_results', []))} 个")
                
                # 显示最终报告的统计信息
                final_report = results.get("final_report", "")
                if final_report:
                    report_length = len(final_report)
                    word_count = len(final_report.split())
                    print(f"📝 报告统计: {report_length} 字符, 约 {word_count} 词")
                    print(f"📄 报告预览: {final_report[:300]}...")

                    # 检查是否达到5000字要求
                    if report_length >= 5000:
                        print("✅ 报告长度达标 (≥5000字符)")
                    else:
                        print(f"⚠️ 报告长度不足 ({report_length}/5000字符)")
                else:
                    print("⚠️ 没有生成最终报告")
                    
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        
        print("\n" + "-"*30 + " 测试完成 " + "-"*30)

if __name__ == "__main__":
    test_search()
