"""
测试搜索功能的简单脚本
"""
from workflow import multi_agent_workflow
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def test_search():
    """测试搜索功能"""
    print("🔍 开始测试搜索功能...")
    
    # 测试查询
    test_queries = [
        "人工智能在医疗领域的最新应用",
        "2024年新能源汽车发展趋势",
        "量子计算技术突破"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*50}")
        print(f"测试 {i}: {query}")
        print('='*50)
        
        try:
            # 执行搜索
            results = multi_agent_workflow.run_search(query, max_rounds=2)
            
            if results.get("error"):
                print(f"❌ 搜索失败: {results['error']}")
            else:
                print(f"✅ 搜索成功!")
                print(f"📊 找到结果: {len(results.get('search_results', []))} 个")
                
                # 显示最终报告的前200个字符
                final_report = results.get("final_report", "")
                if final_report:
                    print(f"📝 报告预览: {final_report[:200]}...")
                else:
                    print("⚠️ 没有生成最终报告")
                    
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        
        print("\n" + "-"*30 + " 测试完成 " + "-"*30)

if __name__ == "__main__":
    test_search()
