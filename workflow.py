"""
LangGraph工作流模块 - 定义多智能体协作的工作流程
"""
from typing import Dict, Any, List, Literal, TypedDict, Annotated
from langgraph.graph import StateGraph, START, END, MessagesState
from langgraph.types import Command
from agents import (
    agent_factory, 
    create_supervisor_agent,
    transfer_to_search,
    transfer_to_analysis, 
    transfer_to_synthesis
)
import operator
import logging

logger = logging.getLogger(__name__)

class SearchState(TypedDict):
    """搜索状态定义"""
    messages: Annotated[List, operator.add]
    current_agent: str
    search_results: List[Dict[str, Any]]
    analysis_results: Dict[str, Any]
    final_report: str
    search_round: int
    max_rounds: int
    user_query: str
    search_history: List[str]

class MultiAgentSearchWorkflow:
    """多智能体搜索工作流"""
    
    def __init__(self):
        self.search_agent = agent_factory.create_search_agent()
        self.analysis_agent = agent_factory.create_analysis_agent()
        self.synthesis_agent = agent_factory.create_synthesis_agent()
        self.supervisor_agent = create_supervisor_agent()
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """构建工作流图"""
        
        # 定义节点函数
        def supervisor_node(state: SearchState) -> Command[Literal["search_agent", "analysis_agent", "synthesis_agent", END]]:
            """监督节点 - 协调整个搜索流程"""
            try:
                # 调用监督智能体
                result = self.supervisor_agent.invoke(state)

                # 检查是否有工具调用
                last_message = result["messages"][-1] if result["messages"] else None
                if last_message and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    # 有工具调用，让智能体自己处理
                    return Command(
                        update={"messages": result["messages"]},
                        goto="supervisor"  # 继续在supervisor中处理
                    )

                # 没有工具调用，检查是否应该结束
                if state.get("search_round", 0) >= state.get("max_rounds", 3):
                    return Command(
                        update={"messages": result["messages"]},
                        goto=END
                    )

                # 默认开始搜索
                return Command(
                    update={"messages": result["messages"]},
                    goto="search_agent"
                )

            except Exception as e:
                logger.error(f"监督节点错误: {str(e)}")
                return Command(
                    update={"messages": [{"role": "assistant", "content": f"处理过程中出现错误: {str(e)}"}]},
                    goto=END
                )
        
        def search_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """搜索节点 - 执行搜索任务"""
            try:
                # 调用搜索智能体
                result = self.search_agent.invoke(state)
                
                # 提取搜索结果
                search_results = []
                for message in result["messages"]:
                    if hasattr(message, 'content') and "搜索" in message.content:
                        # 这里可以解析搜索结果
                        search_results.append({
                            "content": message.content,
                            "timestamp": message.additional_kwargs.get("timestamp", "")
                        })
                
                updated_state = {
                    "messages": result["messages"],
                    "search_results": state.get("search_results", []) + search_results,
                    "current_agent": "search_agent",
                    "search_round": state.get("search_round", 0) + 1
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"搜索节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"搜索过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def analysis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """分析节点 - 分析搜索结果"""
            try:
                # 调用分析智能体
                result = self.analysis_agent.invoke(state)
                
                # 提取分析结果
                analysis_results = {
                    "summary": "分析完成",
                    "key_points": [],
                    "recommendations": []
                }
                
                # 解析分析结果
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        analysis_results["summary"] = message.content[:200] + "..."
                
                updated_state = {
                    "messages": result["messages"],
                    "analysis_results": analysis_results,
                    "current_agent": "analysis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"分析节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"分析过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def synthesis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """综合节点 - 生成最终报告"""
            try:
                # 调用综合智能体
                result = self.synthesis_agent.invoke(state)
                
                # 生成最终报告
                final_report = ""
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        final_report += message.content + "\n"
                
                updated_state = {
                    "messages": result["messages"],
                    "final_report": final_report,
                    "current_agent": "synthesis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"综合节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"综合过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        # 构建状态图
        workflow = StateGraph(SearchState)
        
        # 添加节点
        workflow.add_node("supervisor", supervisor_node)
        workflow.add_node("search_agent", search_node)
        workflow.add_node("analysis_agent", analysis_node)
        workflow.add_node("synthesis_agent", synthesis_node)
        
        # 添加边
        workflow.add_edge(START, "supervisor")
        
        # 编译工作流
        return workflow.compile()
    
    def run_search(self, query: str, max_rounds: int = 3) -> Dict[str, Any]:
        """运行搜索工作流 - 简化版本直接调用搜索"""
        try:
            # 直接执行搜索而不使用复杂的工作流
            from search_tools import search_manager

            # 执行搜索
            search_results = []
            try:
                # 同步调用搜索
                response = search_manager.tavily_client.search(
                    query=query,
                    max_results=max_rounds * 3,
                    include_answer=True,
                    include_raw_content=True
                )

                for result in response.get('results', []):
                    search_results.append({
                        'title': result.get('title', ''),
                        'url': result.get('url', ''),
                        'content': result.get('content', ''),
                        'score': result.get('score', 0),
                        'published_date': result.get('published_date', '')
                    })

                logger.info(f"搜索完成: {query}, 找到 {len(search_results)} 个结果")

            except Exception as search_error:
                logger.error(f"搜索失败: {str(search_error)}")
                search_results = []

            # 生成简单的分析报告
            if search_results:
                final_report = f"## 🔍 搜索结果报告\n\n"
                final_report += f"**查询**: {query}\n\n"
                final_report += f"**找到结果**: {len(search_results)} 条\n\n"

                final_report += "### 📋 主要发现\n\n"
                for i, result in enumerate(search_results[:5], 1):
                    final_report += f"**{i}. {result['title']}**\n"
                    final_report += f"   - 链接: {result['url']}\n"
                    final_report += f"   - 摘要: {result['content'][:150]}...\n\n"

                final_report += "### 💡 总结\n\n"
                final_report += f"基于搜索结果，关于「{query}」的信息已经收集完成。"
                final_report += f"共找到{len(search_results)}个相关结果，涵盖了该主题的多个方面。"
            else:
                final_report = f"抱歉，没有找到关于「{query}」的相关信息。请尝试使用不同的关键词。"

            return {
                "messages": [
                    {"role": "user", "content": query},
                    {"role": "assistant", "content": final_report}
                ],
                "search_results": search_results,
                "final_report": final_report,
                "search_round": 1,
                "user_query": query
            }

        except Exception as e:
            logger.error(f"工作流执行错误: {str(e)}")
            return {
                "error": str(e),
                "messages": [
                    {"role": "user", "content": query},
                    {"role": "assistant", "content": f"搜索过程中出现错误: {str(e)}"}
                ]
            }
    
    def stream_search(self, query: str, max_rounds: int = 3):
        """流式执行搜索工作流"""
        initial_state = {
            "messages": [{"role": "user", "content": query}],
            "current_agent": "supervisor",
            "search_results": [],
            "analysis_results": {},
            "final_report": "",
            "search_round": 0,
            "max_rounds": max_rounds,
            "user_query": query,
            "search_history": []
        }
        
        try:
            # 流式执行工作流
            for chunk in self.workflow.stream(initial_state):
                yield chunk
        except Exception as e:
            logger.error(f"流式工作流执行错误: {str(e)}")
            yield {
                "error": str(e),
                "messages": [{"role": "assistant", "content": f"执行过程中出现错误: {str(e)}"}]
            }

# 创建全局工作流实例
multi_agent_workflow = MultiAgentSearchWorkflow()
