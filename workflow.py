"""
LangGraph工作流模块 - 定义多智能体协作的工作流程
"""
from typing import Dict, Any, List, Literal, TypedDict, Annotated
from langgraph.graph import StateGraph, START, END, MessagesState
from langgraph.types import Command
from agents import (
    agent_factory,
    create_supervisor_agent,
    transfer_to_search,
    transfer_to_analysis,
    transfer_to_synthesis
)
import operator
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class SearchState(TypedDict):
    """搜索状态定义"""
    messages: Annotated[List, operator.add]
    current_agent: str
    search_results: List[Dict[str, Any]]
    analysis_results: Dict[str, Any]
    final_report: str
    search_round: int
    max_rounds: int
    user_query: str
    search_history: List[str]

class MultiAgentSearchWorkflow:
    """多智能体搜索工作流"""
    
    def __init__(self):
        self.search_agent = agent_factory.create_search_agent()
        self.analysis_agent = agent_factory.create_analysis_agent()
        self.synthesis_agent = agent_factory.create_synthesis_agent()
        self.supervisor_agent = create_supervisor_agent()
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """构建工作流图"""
        
        # 定义节点函数
        def supervisor_node(state: SearchState) -> Command[Literal["search_agent", "analysis_agent", "synthesis_agent", END]]:
            """监督节点 - 协调整个搜索流程"""
            try:
                # 调用监督智能体
                result = self.supervisor_agent.invoke(state)

                # 检查是否有工具调用
                last_message = result["messages"][-1] if result["messages"] else None
                if last_message and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    # 有工具调用，让智能体自己处理
                    return Command(
                        update={"messages": result["messages"]},
                        goto="supervisor"  # 继续在supervisor中处理
                    )

                # 没有工具调用，检查是否应该结束
                if state.get("search_round", 0) >= state.get("max_rounds", 3):
                    return Command(
                        update={"messages": result["messages"]},
                        goto=END
                    )

                # 默认开始搜索
                return Command(
                    update={"messages": result["messages"]},
                    goto="search_agent"
                )

            except Exception as e:
                logger.error(f"监督节点错误: {str(e)}")
                return Command(
                    update={"messages": [{"role": "assistant", "content": f"处理过程中出现错误: {str(e)}"}]},
                    goto=END
                )
        
        def search_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """搜索节点 - 执行搜索任务"""
            try:
                # 调用搜索智能体
                result = self.search_agent.invoke(state)
                
                # 提取搜索结果
                search_results = []
                for message in result["messages"]:
                    if hasattr(message, 'content') and "搜索" in message.content:
                        # 这里可以解析搜索结果
                        search_results.append({
                            "content": message.content,
                            "timestamp": message.additional_kwargs.get("timestamp", "")
                        })
                
                updated_state = {
                    "messages": result["messages"],
                    "search_results": state.get("search_results", []) + search_results,
                    "current_agent": "search_agent",
                    "search_round": state.get("search_round", 0) + 1
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"搜索节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"搜索过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def analysis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """分析节点 - 分析搜索结果"""
            try:
                # 调用分析智能体
                result = self.analysis_agent.invoke(state)
                
                # 提取分析结果
                analysis_results = {
                    "summary": "分析完成",
                    "key_points": [],
                    "recommendations": []
                }
                
                # 解析分析结果
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        analysis_results["summary"] = message.content[:200] + "..."
                
                updated_state = {
                    "messages": result["messages"],
                    "analysis_results": analysis_results,
                    "current_agent": "analysis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"分析节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"分析过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def synthesis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """综合节点 - 生成最终报告"""
            try:
                # 调用综合智能体
                result = self.synthesis_agent.invoke(state)
                
                # 生成最终报告
                final_report = ""
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        final_report += message.content + "\n"
                
                updated_state = {
                    "messages": result["messages"],
                    "final_report": final_report,
                    "current_agent": "synthesis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"综合节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"综合过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        # 构建状态图
        workflow = StateGraph(SearchState)
        
        # 添加节点
        workflow.add_node("supervisor", supervisor_node)
        workflow.add_node("search_agent", search_node)
        workflow.add_node("analysis_agent", analysis_node)
        workflow.add_node("synthesis_agent", synthesis_node)
        
        # 添加边
        workflow.add_edge(START, "supervisor")
        
        # 编译工作流
        return workflow.compile()

    def _generate_fallback_report(self, query: str, results: List[Dict[str, Any]], search_summary: List[Dict[str, Any]]) -> str:
        """生成回退报告（当LLM分析失败时使用）"""
        report = f"# 📋 搜索结果汇总报告：{query}\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
        report += f"**搜索状态**: LLM分析失败，使用基础汇总模式\n"
        report += f"**数据来源**: {len(results)} 个信息源\n\n"
        report += "---\n\n"

        # 搜索概况
        report += "## 🔍 搜索概况\n\n"
        for summary in search_summary:
            status = "✅" if not summary.get('error') else "❌"
            report += f"- {status} **第{summary['round']}轮**: {summary['query']} ({summary['results_count']}个结果)\n"
        report += "\n"

        # 主要发现
        report += "## 📊 主要发现\n\n"
        for i, result in enumerate(results[:10], 1):
            report += f"### {i}. {result['title']}\n"
            report += f"**来源**: {result['url']}\n"
            if result.get('published_date'):
                report += f"**时间**: {result['published_date']}\n"
            report += f"**相关性**: {result.get('score', 0):.2f}\n"
            report += f"**内容摘要**: {result.get('content', '')[:300]}...\n\n"

        # 信息来源汇总
        report += "## 📚 完整信息来源\n\n"
        for i, result in enumerate(results, 1):
            report += f"{i}. [{result['title']}]({result['url']})\n"

        return report
    
    def run_search(self, query: str, max_rounds: int = 3) -> Dict[str, Any]:
        """运行搜索工作流 - 增强版本，提供更广泛和深入的搜索"""
        try:
            # 导入必要的模块
            from search_tools import search_manager
            from langchain_openai import ChatOpenAI
            from langchain_core.messages import HumanMessage, SystemMessage
            from config import Config
            from utils import query_processor

            # 生成多个相关查询以增加搜索广度
            related_queries = query_processor.generate_related_queries(query)
            all_queries = [query] + related_queries[:4]  # 主查询 + 4个相关查询

            logger.info(f"开始多轮搜索，共 {len(all_queries)} 个查询")

            # 执行多轮搜索
            all_search_results = []
            search_summary = []

            for i, search_query in enumerate(all_queries, 1):
                try:
                    logger.info(f"执行第 {i} 轮搜索: {search_query}")

                    # 每轮搜索获取更多结果
                    response = search_manager.tavily_client.search(
                        query=search_query,
                        max_results=15,  # 每轮搜索15个结果
                        include_answer=True,
                        include_raw_content=True,
                        search_depth="advanced"  # 使用高级搜索模式
                    )

                    round_results = []
                    for result in response.get('results', []):
                        processed_result = {
                            'title': result.get('title', ''),
                            'url': result.get('url', ''),
                            'content': result.get('content', ''),
                            'raw_content': result.get('raw_content', '')[:2000],  # 增加原始内容长度
                            'score': result.get('score', 0),
                            'published_date': result.get('published_date', ''),
                            'search_query': search_query,
                            'search_round': i
                        }
                        round_results.append(processed_result)

                    all_search_results.extend(round_results)
                    search_summary.append({
                        'query': search_query,
                        'results_count': len(round_results),
                        'round': i
                    })

                    logger.info(f"第 {i} 轮搜索完成: {search_query}, 找到 {len(round_results)} 个结果")

                except Exception as search_error:
                    logger.error(f"第 {i} 轮搜索失败: {search_query}, 错误: {str(search_error)}")
                    search_summary.append({
                        'query': search_query,
                        'results_count': 0,
                        'round': i,
                        'error': str(search_error)
                    })

            # 去重和排序
            from utils import result_processor
            unique_results = result_processor.deduplicate_results(all_search_results)
            ranked_results = result_processor.rank_results(unique_results, query)

            logger.info(f"搜索汇总: 总共找到 {len(all_search_results)} 个结果，去重后 {len(unique_results)} 个")

            # 使用LLM分析搜索结果并生成详细回答
            if ranked_results:
                # 准备更丰富的搜索内容用于LLM分析
                search_content = f"=== 搜索概况 ===\n"
                search_content += f"主查询: {query}\n"
                search_content += f"相关查询: {', '.join(related_queries[:4])}\n"
                search_content += f"总结果数: {len(ranked_results)}\n"
                search_content += f"搜索轮次: {len(all_queries)}\n\n"

                # 添加搜索统计信息
                search_content += "=== 各轮搜索统计 ===\n"
                for summary in search_summary:
                    if summary.get('error'):
                        search_content += f"第{summary['round']}轮 - {summary['query']}: 失败 ({summary['error']})\n"
                    else:
                        search_content += f"第{summary['round']}轮 - {summary['query']}: {summary['results_count']}个结果\n"
                search_content += "\n"

                # 使用更多搜索结果进行分析（前20个）
                for i, result in enumerate(ranked_results[:20], 1):
                    search_content += f"\n=== 搜索结果 {i} (第{result.get('search_round', 1)}轮) ===\n"
                    search_content += f"标题: {result['title']}\n"
                    search_content += f"来源: {result['url']}\n"
                    search_content += f"查询: {result.get('search_query', query)}\n"
                    search_content += f"相关性分数: {result.get('score', 0)}\n"
                    if result.get('published_date'):
                        search_content += f"发布时间: {result['published_date']}\n"

                    # 使用更长的内容进行分析
                    content = result.get('raw_content') or result.get('content', '')
                    search_content += f"内容: {content[:1500]}\n"  # 增加到1500字符
                    search_content += f"内容摘要: {result.get('content', '')[:300]}\n"
                    search_content += "\n"

                # 创建LLM实例，增加token限制
                llm = ChatOpenAI(
                    api_key=Config.OPENAI_API_KEY,
                    base_url=Config.MODEL_BASE_URL,
                    model=Config.MODEL_NAME,
                    temperature=0.2,  # 进一步降低温度以获得更准确的回答
                    max_tokens=4000   # 增加token限制以支持更长的回答
                )

                # 构建详细分析提示
                system_prompt = """你是一个资深的研究分析师和信息整合专家，具有深厚的跨领域知识背景。你的专长是从大量复杂的搜索结果中提取、分析和整合关键信息，并生成高质量的综合性报告。

核心能力：
1. 深度信息提取：从多个来源中识别和提取关键信息点
2. 逻辑分析：建立信息间的逻辑关系和因果联系
3. 批判性思维：评估信息的可靠性和权威性
4. 综合整合：将分散的信息整合成连贯的知识体系
5. 趋势洞察：识别发展趋势和未来方向

分析标准：
- 信息准确性：确保所有事实和数据的准确性
- 内容全面性：涵盖问题的各个重要方面
- 逻辑严密性：保持论述的逻辑一致性
- 观点平衡性：呈现不同角度和观点
- 实用价值：提供有价值的洞察和建议

输出要求：
- 使用专业而易懂的中文表达
- 内容详实，不少于5000字
- 结构层次分明，逻辑清晰
- 包含具体数据、案例和引用
- 标注信息来源和时效性"""

                user_prompt = f"""研究主题: {query}

基于以下多轮搜索获得的丰富信息，请生成一份详细的综合性分析报告：

{search_content}

报告结构要求（总字数不少于5000字）：

## 1. 执行摘要 (300-500字)
- 核心发现和关键结论
- 主要趋势和重要数据
- 报告的价值和意义

## 2. 背景与现状分析 (1000-1500字)
- 问题的背景和重要性
- 当前发展现状
- 市场/行业/技术环境
- 关键参与者和利益相关方

## 3. 深度分析 (2000-2500字)
- 核心问题的多维度分析
- 技术/商业/社会等各个层面的详细解读
- 成功案例和失败教训
- 数据趋势和统计分析
- 国内外对比分析

## 4. 挑战与机遇 (800-1000字)
- 面临的主要挑战和障碍
- 潜在的发展机遇
- 风险评估和应对策略
- 不确定性因素分析

## 5. 未来展望与建议 (600-800字)
- 发展趋势预测
- 战略建议和行动方案
- 政策建议（如适用）
- 投资和发展方向

## 6. 结论 (200-300字)
- 核心观点总结
- 关键启示
- 后续研究方向

注意事项：
1. 每个部分都要有具体的数据支撑
2. 引用具体的信息来源
3. 保持客观中立的分析态度
4. 突出创新点和独特见解
5. 确保内容的时效性和准确性"""

                try:
                    # 调用LLM进行深度分析
                    messages = [
                        SystemMessage(content=system_prompt),
                        HumanMessage(content=user_prompt)
                    ]

                    logger.info("开始LLM深度分析，预计生成5000+字报告...")
                    response = llm.invoke(messages)
                    ai_analysis = response.content

                    # 构建完整的分析报告
                    final_report = f"# 📊 深度研究报告：{query}\n\n"
                    final_report += f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
                    final_report += f"**数据来源**: {len(ranked_results)} 个权威信息源\n"
                    final_report += f"**搜索范围**: {len(all_queries)} 轮多维度搜索\n"
                    final_report += f"**报告字数**: 约 {len(ai_analysis)} 字\n\n"
                    final_report += "---\n\n"

                    # 添加AI分析内容
                    final_report += ai_analysis

                    # 添加详细的参考来源
                    final_report += "\n\n---\n\n"
                    final_report += "# 📚 详细参考来源\n\n"
                    final_report += f"本报告基于 {len(ranked_results)} 个高质量信息源，通过多轮搜索获得：\n\n"

                    # 按搜索轮次组织参考来源
                    sources_by_round = {}
                    for result in ranked_results[:15]:  # 显示前15个最相关的来源
                        round_num = result.get('search_round', 1)
                        if round_num not in sources_by_round:
                            sources_by_round[round_num] = []
                        sources_by_round[round_num].append(result)

                    for round_num in sorted(sources_by_round.keys()):
                        final_report += f"## 第{round_num}轮搜索来源\n"
                        query_for_round = next((s['query'] for s in search_summary if s['round'] == round_num), query)
                        final_report += f"**搜索查询**: {query_for_round}\n\n"

                        for i, result in enumerate(sources_by_round[round_num], 1):
                            final_report += f"**[{round_num}-{i}] {result['title']}**\n"
                            final_report += f"   🔗 链接: {result['url']}\n"
                            final_report += f"   📊 相关性: {result.get('score', 0):.2f}\n"
                            if result.get('published_date'):
                                final_report += f"   📅 发布时间: {result['published_date']}\n"
                            final_report += f"   📝 摘要: {result.get('content', '')[:150]}...\n\n"

                    # 添加搜索统计信息
                    final_report += "\n## 📈 搜索统计信息\n\n"
                    final_report += f"- **总搜索轮数**: {len(all_queries)}\n"
                    final_report += f"- **总结果数**: {len(all_search_results)}\n"
                    final_report += f"- **去重后结果数**: {len(ranked_results)}\n"
                    final_report += f"- **分析结果数**: {min(20, len(ranked_results))}\n"
                    final_report += f"- **参考来源数**: {min(15, len(ranked_results))}\n\n"

                    final_report += "### 各轮搜索详情\n"
                    for summary in search_summary:
                        status = "✅ 成功" if not summary.get('error') else f"❌ 失败: {summary['error']}"
                        final_report += f"- **第{summary['round']}轮**: {summary['query']} - {summary['results_count']}个结果 - {status}\n"

                    logger.info(f"深度分析报告生成完成: {query}, 报告长度: {len(final_report)} 字符")

                except Exception as llm_error:
                    logger.error(f"LLM深度分析失败: {str(llm_error)}")
                    # 如果LLM分析失败，生成详细的搜索结果总结
                    final_report = self._generate_fallback_report(query, ranked_results, search_summary)
            else:
                final_report = f"抱歉，经过 {len(all_queries)} 轮搜索，没有找到关于「{query}」的相关信息。\n\n"
                final_report += "建议尝试：\n"
                final_report += "- 使用更具体的关键词\n"
                final_report += "- 尝试不同的表达方式\n"
                final_report += "- 检查拼写是否正确\n"
                final_report += "- 使用更通用的术语"

            return {
                "messages": [
                    {"role": "user", "content": query},
                    {"role": "assistant", "content": final_report}
                ],
                "search_results": ranked_results,
                "final_report": final_report,
                "search_round": len(all_queries),
                "user_query": query
            }

        except Exception as e:
            logger.error(f"工作流执行错误: {str(e)}")
            return {
                "error": str(e),
                "messages": [
                    {"role": "user", "content": query},
                    {"role": "assistant", "content": f"搜索过程中出现错误: {str(e)}"}
                ]
            }
    
    def stream_search(self, query: str, max_rounds: int = 3):
        """流式执行搜索工作流"""
        initial_state = {
            "messages": [{"role": "user", "content": query}],
            "current_agent": "supervisor",
            "search_results": [],
            "analysis_results": {},
            "final_report": "",
            "search_round": 0,
            "max_rounds": max_rounds,
            "user_query": query,
            "search_history": []
        }
        
        try:
            # 流式执行工作流
            for chunk in self.workflow.stream(initial_state):
                yield chunk
        except Exception as e:
            logger.error(f"流式工作流执行错误: {str(e)}")
            yield {
                "error": str(e),
                "messages": [{"role": "assistant", "content": f"执行过程中出现错误: {str(e)}"}]
            }

# 创建全局工作流实例
multi_agent_workflow = MultiAgentSearchWorkflow()
