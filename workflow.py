"""
LangGraph工作流模块 - 定义多智能体协作的工作流程
"""
from typing import Dict, Any, List, Literal, TypedDict, Annotated
from langgraph.graph import StateGraph, START, END, MessagesState
from langgraph.types import Command
from agents import (
    agent_factory, 
    create_supervisor_agent,
    transfer_to_search,
    transfer_to_analysis, 
    transfer_to_synthesis
)
import operator
import logging

logger = logging.getLogger(__name__)

class SearchState(TypedDict):
    """搜索状态定义"""
    messages: Annotated[List, operator.add]
    current_agent: str
    search_results: List[Dict[str, Any]]
    analysis_results: Dict[str, Any]
    final_report: str
    search_round: int
    max_rounds: int
    user_query: str
    search_history: List[str]

class MultiAgentSearchWorkflow:
    """多智能体搜索工作流"""
    
    def __init__(self):
        self.search_agent = agent_factory.create_search_agent()
        self.analysis_agent = agent_factory.create_analysis_agent()
        self.synthesis_agent = agent_factory.create_synthesis_agent()
        self.supervisor_agent = create_supervisor_agent()
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """构建工作流图"""
        
        # 定义节点函数
        def supervisor_node(state: SearchState) -> Command[Literal["search_agent", "analysis_agent", "synthesis_agent", END]]:
            """监督节点 - 协调整个搜索流程"""
            try:
                # 调用监督智能体
                result = self.supervisor_agent.invoke(state)
                
                # 更新状态
                updated_state = {
                    "messages": result["messages"],
                    "current_agent": "supervisor"
                }
                
                # 决定下一步行动
                last_message = result["messages"][-1] if result["messages"] else None
                if last_message and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    tool_call = last_message.tool_calls[0]
                    if "search" in tool_call["name"]:
                        return Command(update=updated_state, goto="search_agent")
                    elif "analysis" in tool_call["name"]:
                        return Command(update=updated_state, goto="analysis_agent")
                    elif "synthesis" in tool_call["name"]:
                        return Command(update=updated_state, goto="synthesis_agent")
                
                # 默认结束
                return Command(update=updated_state, goto=END)
                
            except Exception as e:
                logger.error(f"监督节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"处理过程中出现错误: {str(e)}"}]}, goto=END)
        
        def search_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """搜索节点 - 执行搜索任务"""
            try:
                # 调用搜索智能体
                result = self.search_agent.invoke(state)
                
                # 提取搜索结果
                search_results = []
                for message in result["messages"]:
                    if hasattr(message, 'content') and "搜索" in message.content:
                        # 这里可以解析搜索结果
                        search_results.append({
                            "content": message.content,
                            "timestamp": message.additional_kwargs.get("timestamp", "")
                        })
                
                updated_state = {
                    "messages": result["messages"],
                    "search_results": state.get("search_results", []) + search_results,
                    "current_agent": "search_agent",
                    "search_round": state.get("search_round", 0) + 1
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"搜索节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"搜索过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def analysis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """分析节点 - 分析搜索结果"""
            try:
                # 调用分析智能体
                result = self.analysis_agent.invoke(state)
                
                # 提取分析结果
                analysis_results = {
                    "summary": "分析完成",
                    "key_points": [],
                    "recommendations": []
                }
                
                # 解析分析结果
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        analysis_results["summary"] = message.content[:200] + "..."
                
                updated_state = {
                    "messages": result["messages"],
                    "analysis_results": analysis_results,
                    "current_agent": "analysis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"分析节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"分析过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        def synthesis_node(state: SearchState) -> Command[Literal["supervisor"]]:
            """综合节点 - 生成最终报告"""
            try:
                # 调用综合智能体
                result = self.synthesis_agent.invoke(state)
                
                # 生成最终报告
                final_report = ""
                for message in result["messages"]:
                    if hasattr(message, 'content'):
                        final_report += message.content + "\n"
                
                updated_state = {
                    "messages": result["messages"],
                    "final_report": final_report,
                    "current_agent": "synthesis_agent"
                }
                
                return Command(update=updated_state, goto="supervisor")
                
            except Exception as e:
                logger.error(f"综合节点错误: {str(e)}")
                return Command(update={"messages": [{"role": "assistant", "content": f"综合过程中出现错误: {str(e)}"}]}, goto="supervisor")
        
        # 构建状态图
        workflow = StateGraph(SearchState)
        
        # 添加节点
        workflow.add_node("supervisor", supervisor_node)
        workflow.add_node("search_agent", search_node)
        workflow.add_node("analysis_agent", analysis_node)
        workflow.add_node("synthesis_agent", synthesis_node)
        
        # 添加边
        workflow.add_edge(START, "supervisor")
        
        # 编译工作流
        return workflow.compile()
    
    def run_search(self, query: str, max_rounds: int = 3) -> Dict[str, Any]:
        """运行搜索工作流"""
        initial_state = {
            "messages": [{"role": "user", "content": query}],
            "current_agent": "supervisor",
            "search_results": [],
            "analysis_results": {},
            "final_report": "",
            "search_round": 0,
            "max_rounds": max_rounds,
            "user_query": query,
            "search_history": []
        }
        
        try:
            # 执行工作流
            result = self.workflow.invoke(initial_state)
            return result
        except Exception as e:
            logger.error(f"工作流执行错误: {str(e)}")
            return {
                "error": str(e),
                "messages": [{"role": "assistant", "content": f"执行过程中出现错误: {str(e)}"}]
            }
    
    def stream_search(self, query: str, max_rounds: int = 3):
        """流式执行搜索工作流"""
        initial_state = {
            "messages": [{"role": "user", "content": query}],
            "current_agent": "supervisor",
            "search_results": [],
            "analysis_results": {},
            "final_report": "",
            "search_round": 0,
            "max_rounds": max_rounds,
            "user_query": query,
            "search_history": []
        }
        
        try:
            # 流式执行工作流
            for chunk in self.workflow.stream(initial_state):
                yield chunk
        except Exception as e:
            logger.error(f"流式工作流执行错误: {str(e)}")
            yield {
                "error": str(e),
                "messages": [{"role": "assistant", "content": f"执行过程中出现错误: {str(e)}"}]
            }

# 创建全局工作流实例
multi_agent_workflow = MultiAgentSearchWorkflow()
