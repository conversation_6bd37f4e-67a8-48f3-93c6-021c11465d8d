"""
工具模块 - 提供各种辅助功能
"""
import re
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ResultProcessor:
    """结果处理器 - 处理和格式化搜索结果"""
    
    @staticmethod
    def deduplicate_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果"""
        seen_urls = set()
        seen_content_hashes = set()
        unique_results = []
        
        for result in results:
            url = result.get('url', '')
            content = result.get('content', '')
            
            # 生成内容哈希
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 检查URL和内容是否重复
            if url not in seen_urls and content_hash not in seen_content_hashes:
                seen_urls.add(url)
                seen_content_hashes.add(content_hash)
                unique_results.append(result)
        
        logger.info(f"去重前: {len(results)} 个结果, 去重后: {len(unique_results)} 个结果")
        return unique_results
    
    @staticmethod
    def rank_results(results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """对搜索结果进行智能排序"""
        def calculate_relevance_score(result: Dict[str, Any]) -> float:
            """计算相关性分数"""
            score = result.get('score', 0.0)
            title = result.get('title', '').lower()
            content = result.get('content', '').lower()
            query_lower = query.lower()
            
            # 基础分数
            relevance_score = float(score)
            
            # 标题匹配加分
            if query_lower in title:
                relevance_score += 0.5
            
            # 内容匹配加分
            content_matches = len(re.findall(re.escape(query_lower), content))
            relevance_score += content_matches * 0.1
            
            # 时效性加分（如果有发布日期）
            published_date = result.get('published_date', '')
            if published_date:
                try:
                    # 简单的时效性评分
                    relevance_score += 0.1
                except:
                    pass
            
            # 内容长度适中加分
            content_length = len(content)
            if 100 <= content_length <= 1000:
                relevance_score += 0.2
            
            return relevance_score
        
        # 按相关性分数排序
        ranked_results = sorted(results, key=calculate_relevance_score, reverse=True)
        
        logger.info(f"结果排序完成，共 {len(ranked_results)} 个结果")
        return ranked_results
    
    @staticmethod
    def extract_key_information(results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从搜索结果中提取关键信息"""
        if not results:
            return {"summary": "无搜索结果", "key_points": [], "sources": []}

        # 提取关键点
        key_points = []
        sources = []
        all_content = ""

        for result in results[:10]:  # 只处理前10个结果
            content = result.get('content', '')
            title = result.get('title', '')
            url = result.get('url', '')

            all_content += content + " "

            if title and url:
                sources.append({
                    "title": title,
                    "url": url,
                    "snippet": content[:200] + "..." if len(content) > 200 else content,
                    "published_date": result.get('published_date', ''),
                    "score": result.get('score', 0)
                })

        # 改进的关键点提取
        sentences = re.split(r'[.!?。！？；;]', all_content)
        for sentence in sentences:
            sentence = sentence.strip()
            # 过滤掉太短或太长的句子，以及包含特定无用词汇的句子
            if (30 <= len(sentence) <= 300 and
                not any(word in sentence.lower() for word in ['cookie', 'privacy', '版权', '免责', 'terms']) and
                any(char.isalnum() for char in sentence)):  # 确保包含字母或数字
                key_points.append(sentence)

        # 去重并按长度排序，选择最有信息量的句子
        key_points = list(set(key_points))
        key_points.sort(key=len, reverse=True)  # 较长的句子通常包含更多信息
        key_points = key_points[:8]  # 增加到8个关键点

        return {
            "summary": f"从 {len(results)} 个搜索结果中提取了 {len(key_points)} 个关键信息点",
            "key_points": key_points,
            "sources": sources[:8],  # 增加源数量
            "total_content_length": len(all_content)
        }

class QueryProcessor:
    """查询处理器 - 处理和优化用户查询"""
    
    @staticmethod
    def clean_query(query: str) -> str:
        """清理和标准化查询"""
        # 移除多余空格
        query = re.sub(r'\s+', ' ', query.strip())
        
        # 移除特殊字符（保留中文、英文、数字、空格）
        query = re.sub(r'[^\w\s\u4e00-\u9fff]', '', query)
        
        return query
    
    @staticmethod
    def generate_related_queries(query: str) -> List[str]:
        """生成相关查询"""
        base_query = QueryProcessor.clean_query(query)
        
        related_queries = [
            f"{base_query} 最新发展",
            f"{base_query} 应用案例",
            f"{base_query} 技术原理",
            f"{base_query} 市场分析",
            f"{base_query} 未来趋势"
        ]
        
        return related_queries
    
    @staticmethod
    def extract_keywords(query: str) -> List[str]:
        """提取查询中的关键词"""
        # 简单的关键词提取
        words = query.split()
        
        # 过滤停用词（简化版）
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过'}
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        
        return keywords[:5]  # 返回前5个关键词

class DisplayFormatter:
    """显示格式化器 - 格式化各种显示内容"""
    
    @staticmethod
    def format_search_progress(current_round: int, max_rounds: int, current_task: str) -> str:
        """格式化搜索进度"""
        progress_bar = "█" * current_round + "░" * (max_rounds - current_round)
        return f"🔄 搜索进度: [{progress_bar}] {current_round}/{max_rounds} - {current_task}"
    
    @staticmethod
    def format_result_summary(results: Dict[str, Any]) -> str:
        """格式化结果摘要"""
        if not results:
            return "❌ 暂无搜索结果"
        
        summary = "## 📊 搜索结果摘要\n\n"
        
        # 基本统计
        search_results_count = len(results.get('search_results', []))
        summary += f"- 🔍 搜索结果: {search_results_count} 条\n"
        
        if results.get('analysis_results'):
            summary += f"- 🧠 分析状态: 已完成\n"
        
        if results.get('final_report'):
            summary += f"- 📝 报告状态: 已生成\n"
        
        summary += f"- ⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        return summary
    
    @staticmethod
    def format_error_message(error: str, context: str = "") -> str:
        """格式化错误消息"""
        formatted_error = f"❌ **错误**: {error}\n\n"
        
        if context:
            formatted_error += f"**上下文**: {context}\n\n"
        
        formatted_error += "**建议**:\n"
        formatted_error += "- 检查网络连接\n"
        formatted_error += "- 尝试简化搜索查询\n"
        formatted_error += "- 稍后重试\n"
        
        return formatted_error
    
    @staticmethod
    def format_sources(sources: List[Dict[str, Any]]) -> str:
        """格式化信息源"""
        if not sources:
            return "暂无信息源"
        
        formatted = "## 📚 信息来源\n\n"
        
        for i, source in enumerate(sources, 1):
            title = source.get('title', '无标题')
            url = source.get('url', '#')
            snippet = source.get('snippet', '无摘要')
            
            formatted += f"**{i}. [{title}]({url})**\n"
            formatted += f"   {snippet}\n\n"
        
        return formatted

# 创建全局实例
result_processor = ResultProcessor()
query_processor = QueryProcessor()
display_formatter = DisplayFormatter()
