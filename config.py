"""
配置文件 - 管理应用的所有配置参数
"""
import os
from dotenv import load_dotenv
from typing import Dict, Any

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # OpenAI API 配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    MODEL_BASE_URL = os.getenv("MODEL_BASE_URL")
    MODEL_NAME = os.getenv("MODEL_NAME", "Qwen/Qwen3-Coder-480B-A35B-Instruct")
    MODEL_PROVIDER = os.getenv("MODEL_PROVIDER", "ModelScope")
    
    # Tavily 搜索引擎配置
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
    
    # 搜索配置
    MAX_SEARCH_RESULTS = 15  # 增加每轮搜索结果数
    MAX_SEARCH_ROUNDS = 5    # 增加搜索轮数
    SEARCH_TIMEOUT = 60      # 增加搜索超时时间

    # 智能体配置
    AGENT_TEMPERATURE = 0.2  # 降低温度以获得更准确的结果
    MAX_TOKENS = 4000        # 大幅增加token限制以支持长报告
    
    # Streamlit 配置
    PAGE_TITLE = "多智能体搜索助手"
    PAGE_ICON = "🔍"
    LAYOUT = "wide"
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """验证配置是否完整"""
        missing_configs = []
        
        if not cls.OPENAI_API_KEY:
            missing_configs.append("OPENAI_API_KEY")
        if not cls.TAVILY_API_KEY:
            missing_configs.append("TAVILY_API_KEY")
            
        return {
            "valid": len(missing_configs) == 0,
            "missing": missing_configs
        }
    
    @classmethod
    def get_llm_config(cls) -> Dict[str, Any]:
        """获取LLM配置"""
        return {
            "api_key": cls.OPENAI_API_KEY,
            "base_url": cls.MODEL_BASE_URL,
            "model": cls.MODEL_NAME,
            "temperature": cls.AGENT_TEMPERATURE,
            "max_tokens": cls.MAX_TOKENS
        }
