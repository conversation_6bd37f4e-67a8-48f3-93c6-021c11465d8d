"""
Streamlit主应用 - 多智能体搜索助手界面
"""
import streamlit as st
import asyncio
import time
from typing import Dict, Any
from config import Config
from workflow import multi_agent_workflow
import logging

# 配置页面
st.set_page_config(
    page_title=Config.PAGE_TITLE,
    page_icon=Config.PAGE_ICON,
    layout=Config.LAYOUT,
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_session_state():
    """初始化会话状态"""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "search_history" not in st.session_state:
        st.session_state.search_history = []
    if "current_results" not in st.session_state:
        st.session_state.current_results = None
    if "search_in_progress" not in st.session_state:
        st.session_state.search_in_progress = False

def display_config_sidebar():
    """显示配置侧边栏"""
    with st.sidebar:
        st.header("⚙️ 参数配置")
        
        # 验证配置
        config_status = Config.validate_config()
        if config_status["valid"]:
            st.success("✅ 配置验证通过")
        else:
            st.error(f"❌ 缺少配置: {', '.join(config_status['missing'])}")
            st.stop()
        
        # 搜索参数
        st.subheader("🔍 搜索设置")
        max_rounds = st.slider(
            "搜索深度等级",
            min_value=1,
            max_value=5,
            value=Config.MAX_SEARCH_ROUNDS,
            help="搜索深度等级：1=基础搜索，5=深度全面搜索"
        )

        max_results = st.slider(
            "信息丰富度",
            min_value=10,
            max_value=25,
            value=Config.MAX_SEARCH_RESULTS,
            help="每轮搜索的信息源数量，数值越高信息越丰富"
        )

        # 添加报告详细程度选项
        report_detail = st.selectbox(
            "报告详细程度",
            options=["标准报告 (2000-3000字)", "详细报告 (4000-5000字)", "深度报告 (5000+字)"],
            index=2,
            help="选择生成报告的详细程度"
        )
        
        # 模型参数
        st.subheader("🤖 模型设置")
        temperature = st.slider(
            "创造性程度",
            min_value=0.0,
            max_value=1.0,
            value=Config.AGENT_TEMPERATURE,
            step=0.1,
            help="控制AI回答的创造性，值越高越有创意"
        )
        
        # 显示当前配置
        st.subheader("📋 当前配置")
        st.json({
            "模型": Config.MODEL_NAME,
            "提供商": Config.MODEL_PROVIDER,
            "最大轮数": max_rounds,
            "最大结果": max_results,
            "温度": temperature
        })
        
        return {
            "max_rounds": max_rounds,
            "max_results": max_results,
            "temperature": temperature,
            "report_detail": report_detail
        }

def display_search_history():
    """显示搜索历史"""
    if st.session_state.search_history:
        st.subheader("📚 搜索历史")
        for i, query in enumerate(reversed(st.session_state.search_history[-5:])):
            if st.button(f"🔄 {query}", key=f"history_{i}"):
                return query
    return None

def format_search_results(results: Dict[str, Any]) -> str:
    """格式化搜索结果"""
    if not results:
        return "暂无搜索结果"

    # 直接返回最终报告，因为它已经包含了完整的分析
    if results.get("final_report"):
        return results["final_report"]

    # 如果没有最终报告，显示基本信息
    formatted = "## 🔍 搜索结果\n\n"

    # 显示搜索结果统计
    if results.get("search_results"):
        search_count = len(results["search_results"])
        formatted += f"📊 找到 **{search_count}** 个相关结果\n\n"

        # 显示前几个结果的摘要
        formatted += "### 🌐 主要发现\n\n"
        for i, result in enumerate(results["search_results"][:3], 1):
            title = result.get('title', '无标题')
            content = result.get('content', '无内容')
            url = result.get('url', '#')

            formatted += f"**{i}. [{title}]({url})**\n"
            formatted += f"   {content[:150]}...\n\n"

    # 显示分析结果
    if results.get("analysis_results"):
        analysis = results["analysis_results"]
        formatted += "### 🧠 分析总结\n"
        formatted += f"{analysis.get('summary', '无分析结果')}\n\n"

    return formatted

def run_search_workflow(query: str, config: Dict[str, Any]):
    """运行搜索工作流"""
    try:
        # 更新配置
        Config.MAX_SEARCH_ROUNDS = config["max_rounds"]
        Config.MAX_SEARCH_RESULTS = config["max_results"]
        Config.AGENT_TEMPERATURE = config["temperature"]
        
        # 执行搜索
        with st.spinner("🤖 智能体正在工作中..."):
            results = multi_agent_workflow.run_search(
                query=query,
                max_rounds=config["max_rounds"]
            )
        
        return results
    
    except Exception as e:
        logger.error(f"搜索执行错误: {str(e)}")
        st.error(f"搜索过程中出现错误: {str(e)}")
        return None

def main():
    """主应用函数"""
    # 初始化会话状态
    init_session_state()
    
    # 显示标题
    st.title("🔍 多智能体搜索助手")
    st.markdown("---")
    
    # 显示配置侧边栏
    config = display_config_sidebar()
    
    # 主界面布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("💬 搜索对话")
        
        # 用户输入
        user_input = st.text_input(
            "请输入您的搜索问题:",
            placeholder="例如: 人工智能在医疗领域的最新应用",
            disabled=st.session_state.search_in_progress
        )
        
        # 搜索按钮
        search_button = st.button(
            "🚀 开始搜索",
            disabled=st.session_state.search_in_progress or not user_input.strip()
        )
        
        # 执行搜索
        if search_button and user_input.strip():
            st.session_state.search_in_progress = True

            # 添加到搜索历史
            if user_input not in st.session_state.search_history:
                st.session_state.search_history.append(user_input)

            # 显示用户消息
            with st.chat_message("user"):
                st.write(user_input)

            # 显示详细搜索进度
            progress_container = st.container()
            with progress_container:
                progress_bar = st.progress(0)
                status_text = st.empty()
                detail_text = st.empty()

                # 显示搜索配置
                detail_text.info(f"🔧 搜索配置：{config['max_rounds']}轮深度搜索，每轮{config['max_results']}个信息源")

                status_text.text("🔍 第1阶段：执行多轮搜索...")
                progress_bar.progress(10)

                # 执行搜索工作流
                results = run_search_workflow(user_input, config)

                status_text.text("� 第2阶段：深度分析与信息整合...")
                progress_bar.progress(60)

                if results:
                    status_text.text("📝 第3阶段：生成详细报告...")
                    progress_bar.progress(90)

                    # 显示报告统计
                    if results.get("final_report"):
                        report_length = len(results["final_report"])
                        detail_text.success(f"📊 报告生成完成：约{report_length}字符，{report_length//500}段落")

                    status_text.text("✅ 所有阶段完成！")
                    progress_bar.progress(100)

                    st.session_state.current_results = results

                    # 清除进度显示
                    progress_container.empty()

                    # 显示助手回复
                    with st.chat_message("assistant"):
                        formatted_results = format_search_results(results)
                        st.markdown(formatted_results)

                        # 显示搜索统计信息
                        if results.get("search_results"):
                            search_count = len(results["search_results"])
                            st.info(f"📊 本次搜索共找到 {search_count} 个相关结果，已进行智能分析和整合")

                    # 添加到消息历史
                    st.session_state.messages.append({
                        "role": "user",
                        "content": user_input
                    })
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": formatted_results
                    })
                else:
                    progress_container.empty()
                    st.error("搜索过程中出现问题，请稍后重试")

            st.session_state.search_in_progress = False
            st.rerun()
        
        # 显示历史对话
        if st.session_state.messages:
            st.subheader("💭 对话历史")
            for message in st.session_state.messages:
                with st.chat_message(message["role"]):
                    st.markdown(message["content"])
    
    with col2:
        st.header("📋 搜索管理")
        
        # 显示搜索历史
        historical_query = display_search_history()
        if historical_query:
            st.session_state.search_in_progress = True
            results = run_search_workflow(historical_query, config)
            if results:
                st.session_state.current_results = results
                with st.chat_message("assistant"):
                    formatted_results = format_search_results(results)
                    st.markdown(formatted_results)
            st.session_state.search_in_progress = False
            st.rerun()
        
        # 清除历史按钮
        if st.button("🗑️ 清除历史"):
            st.session_state.messages = []
            st.session_state.search_history = []
            st.session_state.current_results = None
            st.rerun()
        
        # 显示系统状态
        st.subheader("📊 系统状态")
        status_container = st.container()
        with status_container:
            if st.session_state.search_in_progress:
                st.warning("🔄 搜索进行中...")
            else:
                st.success("✅ 系统就绪")
            
            st.metric("历史搜索", len(st.session_state.search_history))
            st.metric("对话轮数", len(st.session_state.messages) // 2)

if __name__ == "__main__":
    main()
